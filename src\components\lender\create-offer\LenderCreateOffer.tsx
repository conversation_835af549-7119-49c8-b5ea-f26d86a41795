"use client"

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { AlertTriangle, CheckCircle, ArrowLeft, Loader2 } from "lucide-react";
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { createLoanOffer, CreateLoanOfferData } from '@/lib/loan-offers';
import { toast } from 'sonner';

interface LoanOfferData {
  productName: string;
  minAmount: string;
  maxAmount: string;
  minDuration: string;
  maxDuration: string;
  interestRate: string;
  rateType: string;
  description: string;
  targetBorrowers: string[];
  collateralRequired: boolean;
  processingFee: string;
}

export default function LenderCreateOffer() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<LoanOfferData>({
    productName: "",
    minAmount: "700,000",
    maxAmount: "2,000,000",
    minDuration: "1",
    maxDuration: "6",
    interestRate: "3.5",
    rateType: "% Monthly",
    description: "",
    targetBorrowers: [],
    collateralRequired: false,
    processingFee: "2.5",
  });

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    
    try {
      // Validate required fields
      if (!formData.productName.trim()) {
        toast.error('Product name is required');
        return;
      }

      if (!formData.interestRate || parseFloat(formData.interestRate) <= 0) {
        toast.error('Please enter a valid interest rate');
        return;
      }

      if (parseFloat(formData.interestRate) > 7 && formData.rateType === '% Monthly') {
        toast.error('Monthly interest rate cannot exceed 7%');
        return;
      }

      // Validate amount range
      const minAmount = parseFloat(formData.minAmount.replace(/,/g, ''));
      const maxAmount = parseFloat(formData.maxAmount.replace(/,/g, ''));
      
      if (minAmount >= maxAmount) {
        toast.error('Maximum amount must be greater than minimum amount');
        return;
      }

      // Validate duration range
      const minDuration = parseInt(formData.minDuration);
      const maxDuration = parseInt(formData.maxDuration);
      
      if (minDuration >= maxDuration) {
        toast.error('Maximum duration must be greater than minimum duration');
        return;
      }

      // Create loan offer data
      const loanOfferData: CreateLoanOfferData = {
        productName: formData.productName.trim(),
        minAmount: formData.minAmount,
        maxAmount: formData.maxAmount,
        minDuration: formData.minDuration,
        maxDuration: formData.maxDuration,
        interestRate: formData.interestRate,
        rateType: formData.rateType as '% Monthly' | '% Annually',
        description: formData.description.trim(),
        targetBorrowers: formData.targetBorrowers,
        collateralRequired: formData.collateralRequired,
        processingFee: formData.processingFee,
      };

      // Submit to Supabase
      const result = await createLoanOffer(loanOfferData);

      if (result.success) {
        toast.success('Loan offer created successfully!');
        router.push('/dashboard/lender/create-offer/success');
      } else {
        toast.error(result.error || 'Failed to create loan offer');
      }
    } catch (error) {
      console.error('Error submitting loan offer:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const targetBorrowerOptions = [
    "Individual borrowers",
    "Small business owners",
    "Startups",
    "Established businesses",
    "Freelancers",
    "Students"
  ];

  const toggleTargetBorrower = (borrower: string) => {
    setFormData(prev => ({
      ...prev,
      targetBorrowers: prev.targetBorrowers.includes(borrower)
        ? prev.targetBorrowers.filter(b => b !== borrower)
        : [...prev.targetBorrowers, borrower]
    }));
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/lender">
          <Button variant="ghost" size="sm" className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Loan Offer</h1>
          <p className="text-sm text-gray-600">Set up your loan offer parameters for the marketplace</p>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {[1, 2, 3].map((step) => (
          <div key={step} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= step 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {currentStep > step ? <CheckCircle className="w-4 h-4" /> : step}
            </div>
            {step < 3 && (
              <div className={`w-16 h-0.5 mx-2 ${
                currentStep > step ? 'bg-blue-600' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step 1: Basic Loan Parameters */}
      {currentStep === 1 && (
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold text-gray-900">Step 1: Basic Loan Parameters</h2>
            <p className="text-sm text-gray-600">Define the core terms of your loan offer</p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Loan Product Name */}
            <div className="space-y-2">
              <Label htmlFor="productName" className="text-sm font-medium text-gray-700">
                Loan Product Name *
              </Label>
              <Input
                id="productName"
                placeholder="e.g. SME Growth Loan - Monthly"
                value={formData.productName}
                onChange={(e) => setFormData({ ...formData, productName: e.target.value })}
                className="w-full"
              />
            </div>

            {/* Loan Amount Range */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-900">Loan Amount Range *</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minAmount" className="text-xs text-gray-600">
                    Minimum Amount (₦)
                  </Label>
                  <Input
                    id="minAmount"
                    type="number"
                    min="0"
                    step="1000"
                    placeholder="700,000"
                    value={formData.minAmount}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (parseFloat(value) < 0) {
                        toast.error('Amount cannot be negative');
                        return;
                      }
                      setFormData({ ...formData, minAmount: value });
                    }}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxAmount" className="text-xs text-gray-600">
                    Maximum Amount (₦)
                  </Label>
                  <Input
                    id="maxAmount"
                    type="number"
                    min="0"
                    step="1000"
                    placeholder="2,000,000"
                    value={formData.maxAmount}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (parseFloat(value) < 0) {
                        toast.error('Amount cannot be negative');
                        return;
                      }
                      setFormData({ ...formData, maxAmount: value });
                    }}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* Loan Duration Range */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-900">Loan Duration Range *</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">Minimum (months)</Label>
                  <Select
                    value={formData.minDuration}
                    onValueChange={(value) => setFormData({ ...formData, minDuration: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5, 6].map((month) => (
                        <SelectItem key={month} value={month.toString()}>{month}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">Maximum (months)</Label>
                  <Select
                    value={formData.maxDuration}
                    onValueChange={(value) => setFormData({ ...formData, maxDuration: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[6, 12, 18, 24, 36].map((month) => (
                        <SelectItem key={month} value={month.toString()}>{month}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Interest Rate */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">Interest Rate *</Label>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1">
                  <Input
                    placeholder="3.5"
                    value={formData.interestRate}
                    onChange={(e) => setFormData({ ...formData, interestRate: e.target.value })}
                    className="w-full"
                  />
                </div>
                <div className="w-full sm:w-40">
                  <Select
                    value={formData.rateType}
                    onValueChange={(value) => setFormData({ ...formData, rateType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="% Monthly">% Monthly</SelectItem>
                      <SelectItem value="% Annually">% Annually</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertTriangle className="w-4 h-4 text-yellow-600 flex-shrink-0" />
                <span className="text-sm text-yellow-800">Platform cap: Maximum 7% monthly rate allowed</span>
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <Button 
                onClick={handleNext}
                disabled={!formData.productName}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              >
                Next: Additional Details
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Additional Details */}
      {currentStep === 2 && (
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold text-gray-900">Step 2: Additional Details</h2>
            <p className="text-sm text-gray-600">Configure additional loan parameters and target audience</p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Target Borrowers */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">Target Borrowers</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {targetBorrowerOptions.map((borrower) => (
                  <div key={borrower} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={borrower}
                      checked={formData.targetBorrowers.includes(borrower)}
                      onChange={() => toggleTargetBorrower(borrower)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor={borrower} className="text-sm text-gray-700">{borrower}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Processing Fee */}
            <div className="space-y-2">
              <Label htmlFor="processingFee" className="text-sm font-medium text-gray-700">
                Processing Fee (%)
              </Label>
              <Input
                id="processingFee"
                placeholder="2.5"
                value={formData.processingFee}
                onChange={(e) => setFormData({ ...formData, processingFee: e.target.value })}
                className="w-full"
              />
            </div>

            {/* Collateral Requirement */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">Collateral Requirement</Label>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="collateral-required"
                    name="collateral"
                    checked={formData.collateralRequired}
                    onChange={() => setFormData({ ...formData, collateralRequired: true })}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <Label htmlFor="collateral-required" className="text-sm text-gray-700">Collateral Required</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="collateral-not-required"
                    name="collateral"
                    checked={!formData.collateralRequired}
                    onChange={() => setFormData({ ...formData, collateralRequired: false })}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <Label htmlFor="collateral-not-required" className="text-sm text-gray-700">No Collateral Required</Label>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Loan Description
              </Label>
              <Textarea
                id="description"
                placeholder="Describe your loan offer, ideal borrowers, and any special features..."
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full min-h-[100px] resize-none"
              />
            </div>

            <div className="flex justify-between pt-4">
              <Button 
                variant="outline"
                onClick={handleBack}
                className="px-6"
              >
                Back
              </Button>
              <Button 
                onClick={handleNext}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              >
                Next: Review & Confirm
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Review & Confirm */}
      {currentStep === 3 && (
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold text-gray-900">Step 3: Review & Confirm</h2>
            <p className="text-sm text-gray-600">Review your loan offer details before publishing</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <h3 className="font-semibold text-gray-900">Loan Offer Summary</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Product Name</p>
                  <p className="font-medium">{formData.productName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Interest Rate</p>
                  <p className="font-medium">{formData.interestRate} {formData.rateType}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Amount Range</p>
                  <p className="font-medium">₦{formData.minAmount} - ₦{formData.maxAmount}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Duration Range</p>
                  <p className="font-medium">{formData.minDuration} - {formData.maxDuration} months</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Processing Fee</p>
                  <p className="font-medium">{formData.processingFee}%</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Collateral Required</p>
                  <p className="font-medium">{formData.collateralRequired ? 'Yes' : 'No'}</p>
                </div>
              </div>

              {formData.targetBorrowers.length > 0 && (
                <div>
                  <p className="text-sm text-gray-600">Target Borrowers</p>
                  <p className="font-medium">{formData.targetBorrowers.join(', ')}</p>
                </div>
              )}

              {formData.description && (
                <div>
                  <p className="text-sm text-gray-600">Description</p>
                  <p className="font-medium">{formData.description}</p>
                </div>
              )}
            </div>

            <div className="flex justify-between pt-4">
              <Button 
                variant="outline"
                onClick={handleBack}
                disabled={isSubmitting}
                className="px-6"
              >
                Back
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700 text-white px-6"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Loan Offer'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 
