"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { getUserLoanApplications, withdrawApplication } from '@/lib/loan-applications';
import { Database } from '@/lib/supabase';
import { toast } from 'sonner';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  FileText,
  Trash2,
  User,
  Calendar,
  CreditCard,
  DollarSign,
  TrendingUp
} from 'lucide-react';

type LoanApplication = Database['public']['Tables']['loan_applications']['Row'] & {
  loan_offers?: {
    product_name: string;
    interest_rate: number;
    rate_type: string;
    profiles?: {
      individual_accounts?: Array<{ full_name: string }>;
      corporate_accounts?: Array<{ organization_name: string }>;
    };
  };
  lender_name?: string;
};

// Active loans will be fetched from a separate loans table in production
const activeLoans: unknown[] = [];

export default function MyLoansPage() {
  const [applications, setApplications] = useState<LoanApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [withdrawingId, setWithdrawingId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('applications');

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      setIsLoading(true);
      const result = await getUserLoanApplications();
      
      if (result.success) {
        const applications = Array.isArray(result.data) ? result.data : [];
        setApplications(applications);
      } else {
        toast.error(result.error || 'Failed to fetch applications');
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('An error occurred while fetching applications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWithdraw = async (applicationId: string) => {
    if (confirm('Are you sure you want to withdraw this application? This action cannot be undone.')) {
      try {
        setWithdrawingId(applicationId);
        const result = await withdrawApplication(applicationId);
        
        if (result.success) {
          setApplications(prev => prev.map(app => 
            app.id === applicationId ? { ...app, status: 'withdrawn' } : app
          ));
          toast.success('Application withdrawn successfully');
        } else {
          toast.error(result.error || 'Failed to withdraw application');
        }
      } catch (error) {
        console.error('Error withdrawing application:', error);
        toast.error('An error occurred while withdrawing application');
      } finally {
        setWithdrawingId(null);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      case 'withdrawn':
        return <AlertCircle className="w-4 h-4" />;
      case 'active':
        return <TrendingUp className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate summary statistics
  const pendingApplications = applications.filter(app => app.status === 'pending').length;
  const approvedApplications = applications.filter(app => app.status === 'approved').length;
  const totalApplicationAmount = applications
    .filter(app => app.status === 'pending' || app.status === 'approved')
    .reduce((sum, app) => sum + app.requested_amount, 0);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">My Loans</h1>
          <p className="text-gray-500 text-sm mt-1">
            Track your loan applications and manage active loans
          </p>
        </div>
        <Button 
          onClick={() => window.location.href = '/dashboard/borrower/marketplace'}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Apply for New Loan
        </Button>
      </div>

             {/* Summary Cards */}
       <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
         <Card>
           <CardHeader className="pb-2">
             <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
               <FileText className="w-4 h-4" />
               Pending Applications
             </CardTitle>
           </CardHeader>
           <CardContent>
             <div className="text-2xl font-bold">{pendingApplications}</div>
             <p className="text-sm text-gray-500">
               {formatCurrency(totalApplicationAmount)}
             </p>
           </CardContent>
         </Card>
         
         <Card>
           <CardHeader className="pb-2">
             <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
               <CheckCircle className="w-4 h-4" />
               Approved Applications
             </CardTitle>
           </CardHeader>
           <CardContent>
             <div className="text-2xl font-bold">{approvedApplications}</div>
             <p className="text-sm text-gray-500">
               Ready for disbursement
             </p>
           </CardContent>
         </Card>
       </div>

      {/* Tabs for Applications and Active Loans */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                 <TabsList className="grid w-full grid-cols-2">
           <TabsTrigger value="applications">
             Loan Applications ({applications.length})
           </TabsTrigger>
           <TabsTrigger value="active">
             Active Loans ({activeLoans.length})
           </TabsTrigger>
         </TabsList>

        {/* Applications Tab */}
        <TabsContent value="applications">
          {applications.length === 0 ? (
            <Card className="text-center p-8">
              <CardContent>
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Applications Yet</h3>
                <p className="text-gray-500 mb-4">
                  You haven&apos;t submitted any loan applications. Browse the marketplace to find suitable offers.
                </p>
                <Button 
                  onClick={() => window.location.href = '/dashboard/borrower/marketplace'}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Browse Marketplace
                </Button>
              </CardContent>
            </Card>
          ) : (
                         <div className="space-y-3">
               {applications.map((application) => (
                 <Card key={application.id} className="hover:shadow-sm transition-shadow">
                   <CardContent className="p-4">
                     <div className="flex justify-between items-start mb-3">
                       <div className="flex-1">
                         <div className="flex items-center gap-2 mb-1">
                           <h3 className="text-base font-semibold truncate">
                             {application.loan_offers?.product_name || 'Loan Application'}
                           </h3>
                           <Badge 
                             className={`${getStatusColor(application.status)} flex items-center gap-1 text-xs px-2 py-0.5`}
                           >
                             {getStatusIcon(application.status)}
                             {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                           </Badge>
                         </div>
                         <p className="text-xs text-gray-500">
                           {application.lender_name || 'Lender'} • {formatDate(application.created_at)}
                         </p>
                       </div>
                     </div>
                     
                     <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                       <div className="flex items-center gap-1.5">
                         <DollarSign className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                         <div className="min-w-0">
                           <p className="text-xs text-gray-500">Amount</p>
                           <p className="text-sm font-medium truncate">
                             {formatCurrency(application.requested_amount)}
                           </p>
                         </div>
                       </div>
                       
                       <div className="flex items-center gap-1.5">
                         <Calendar className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                         <div className="min-w-0">
                           <p className="text-xs text-gray-500">Duration</p>
                           <p className="text-sm font-medium">
                             {application.requested_duration}mo
                           </p>
                         </div>
                       </div>
                       
                       <div className="flex items-center gap-1.5">
                         <CreditCard className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                         <div className="min-w-0">
                           <p className="text-xs text-gray-500">Rate</p>
                           <p className="text-sm font-medium truncate">
                             {application.loan_offers?.interest_rate}% {application.loan_offers?.rate_type?.slice(0, 2)}
                           </p>
                         </div>
                       </div>
                       
                       <div className="flex items-center gap-1.5">
                         <User className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                         <div className="min-w-0">
                           <p className="text-xs text-gray-500">Guarantor</p>
                           <p className="text-sm font-medium truncate">
                             {application.guarantor_name || 'None'}
                           </p>
                         </div>
                       </div>
                     </div>

                     {application.status === 'rejected' && application.rejection_reason && (
                       <div className="bg-red-50 border border-red-200 rounded p-2 mb-3">
                         <p className="text-xs text-red-700">
                           <strong>Rejected:</strong> {application.rejection_reason}
                         </p>
                       </div>
                     )}

                     <div className="flex justify-between items-center text-xs">
                       <span className="text-gray-400">
                         ID: {application.id.slice(0, 8)}...
                       </span>
                       
                       {application.status === 'pending' && (
                         <Button
                           variant="outline"
                           size="sm"
                           onClick={() => handleWithdraw(application.id)}
                           disabled={withdrawingId === application.id}
                           className="text-red-600 border-red-200 hover:bg-red-50 h-7 px-2 text-xs"
                         >
                           {withdrawingId === application.id ? (
                             <div className="animate-spin rounded-full h-3 w-3 border border-red-600 border-t-transparent"></div>
                           ) : (
                             <>
                               <Trash2 className="w-3 h-3 mr-1" />
                               Withdraw
                             </>
                           )}
                         </Button>
                       )}
                     </div>
                   </CardContent>
                 </Card>
               ))}
             </div>
          )}
        </TabsContent>

                 {/* Active Loans Tab */}
         <TabsContent value="active">
           <Card className="text-center p-6">
             <CardContent className="py-4">
               <TrendingUp className="w-12 h-12 text-gray-300 mx-auto mb-3" />
               <h3 className="text-base font-semibold text-gray-600 mb-2">No Active Loans</h3>
               <p className="text-sm text-gray-500 mb-2">
                 Approved and disbursed loans will appear here for tracking.
               </p>
               <div className="text-xs text-gray-400">
                 View repayment schedules, payment history, and progress.
               </div>
             </CardContent>
           </Card>
         </TabsContent>
      </Tabs>
    </div>
  );
} 