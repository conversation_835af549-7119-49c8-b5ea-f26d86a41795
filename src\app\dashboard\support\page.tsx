"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/lib/supabase";
import { 
  createSupportTicket, 
  getUserTickets, 
  getTicketWithMessages, 
  addMessageToTicket,
  SupportTicket,
  TicketWithMessages 
} from "@/lib/support";
import { toast } from "sonner";

export default function SupportPage() {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<TicketWithMessages | null>(null);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [isNewTicketModalOpen, setIsNewTicketModalOpen] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  
  // New ticket form state
  const [newTicketData, setNewTicketData] = useState<{
    subject: string;
    message: string;
    category: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
  }>({
    subject: "",
    message: "",
    category: "general",
    priority: "medium",
  });

  useEffect(() => {
    fetchUserData();
  }, []);

  useEffect(() => {
    if (userId) {
      fetchTickets();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  const fetchUserData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUserId(user.id);
      }
    } catch (error) {
      console.error('Error fetching user:', error);
    }
  };

  const fetchTickets = async () => {
    if (!userId) return;
    
    setIsLoading(true);
    try {
      const userTickets = await getUserTickets(userId);
      setTickets(userTickets);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast.error('Failed to fetch support tickets');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTicket = async () => {
    if (!userId || !newTicketData.subject.trim() || !newTicketData.message.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSending(true);
    try {
      const result = await createSupportTicket(
        userId,
        newTicketData.subject,
        newTicketData.message,
        newTicketData.category,
        newTicketData.priority
      );

      if (result) {
        toast.success('Support ticket created successfully');
        setIsNewTicketModalOpen(false);
        setNewTicketData({ subject: "", message: "", category: "general", priority: "medium" });
        fetchTickets();
      } else {
        toast.error('Failed to create support ticket');
      }
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast.error('Failed to create support ticket');
    } finally {
      setIsSending(false);
    }
  };

  const handleViewTicket = async (ticketId: string) => {
    if (!userId) return;
    
    try {
      const ticketWithMessages = await getTicketWithMessages(ticketId, userId);
      if (ticketWithMessages) {
        setSelectedTicket(ticketWithMessages);
        setIsTicketModalOpen(true);
      }
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      toast.error('Failed to load ticket details');
    }
  };

  const handleSendMessage = async () => {
    if (!selectedTicket || !userId || !newMessage.trim()) return;

    setIsSending(true);
    try {
      const message = await addMessageToTicket(
        selectedTicket.id,
        userId,
        'user',
        newMessage
      );

      if (message) {
        setSelectedTicket(prev => prev ? {
          ...prev,
          messages: [...prev.messages, message]
        } : null);
        setNewMessage("");
        toast.success('Message sent successfully');
      } else {
        toast.error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsSending(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Support</h1>
          <p className="text-gray-500 text-sm mt-1">Loading your support tickets...</p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Support</h1>
          <p className="text-gray-500 text-sm mt-1">
            Manage your support tickets and get help from our team
          </p>
        </div>
        <Button onClick={() => setIsNewTicketModalOpen(true)}>
          Create New Ticket
        </Button>
      </div>

      {/* Tickets List */}
      {tickets.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500 mb-4">No support tickets yet</p>
            <Button onClick={() => setIsNewTicketModalOpen(true)}>
              Create Your First Ticket
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {tickets.map((ticket) => (
            <Card key={ticket.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-lg">{ticket.subject}</h3>
                  <div className="flex gap-2">
                    <Badge className={getPriorityColor(ticket.priority)}>
                      {ticket.priority}
                    </Badge>
                    <Badge className={getStatusColor(ticket.status)}>
                      {ticket.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-3">Category: {ticket.category}</p>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">
                    Created: {formatTimestamp(ticket.created_at)}
                  </span>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleViewTicket(ticket.id)}
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* New Ticket Modal */}
      <Dialog open={isNewTicketModalOpen} onOpenChange={setIsNewTicketModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Support Ticket</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="subject">Subject *</Label>
              <Input
                id="subject"
                placeholder="Brief description of your issue"
                value={newTicketData.subject}
                onChange={(e) => setNewTicketData(prev => ({ ...prev, subject: e.target.value }))}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={newTicketData.category} 
                  onValueChange={(value) => setNewTicketData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="billing">Billing</SelectItem>
                    <SelectItem value="account">Account</SelectItem>
                    <SelectItem value="loan">Loan</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="priority">Priority</Label>
                                 <Select 
                   value={newTicketData.priority} 
                   onValueChange={(value) => setNewTicketData(prev => ({ ...prev, priority: value as 'low' | 'medium' | 'high' | 'urgent' }))}
                 >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="message">Message *</Label>
              <Textarea
                id="message"
                placeholder="Describe your issue in detail..."
                rows={5}
                value={newTicketData.message}
                onChange={(e) => setNewTicketData(prev => ({ ...prev, message: e.target.value }))}
              />
            </div>
            
            <div className="flex gap-2 justify-end">
              <Button 
                variant="outline" 
                onClick={() => setIsNewTicketModalOpen(false)}
                disabled={isSending}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateTicket} disabled={isSending}>
                {isSending ? 'Creating...' : 'Create Ticket'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Ticket Details Modal */}
      <Dialog open={isTicketModalOpen} onOpenChange={setIsTicketModalOpen}>
        <DialogContent className="max-w-4xl h-[80vh]">
          <DialogHeader>
            <div className="flex justify-between items-start">
              <div>
                <DialogTitle>{selectedTicket?.subject}</DialogTitle>
                <div className="flex gap-2 mt-2">
                  <Badge className={getPriorityColor(selectedTicket?.priority || '')}>
                    {selectedTicket?.priority}
                  </Badge>
                  <Badge className={getStatusColor(selectedTicket?.status || '')}>
                    {selectedTicket?.status.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
            </div>
          </DialogHeader>
          
          {selectedTicket && (
            <div className="flex flex-col h-full">
              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4 pr-2">
                {selectedTicket.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender_type === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.sender_type === "user"
                          ? "bg-indigo-600 text-white"
                          : "bg-gray-100 text-gray-900"
                      }`}
                    >
                      <p className="text-sm whitespace-pre-wrap">{message.message}</p>
                      <span
                        className={`text-xs mt-1 block ${
                          message.sender_type === "user" ? "text-indigo-200" : "text-gray-500"
                        }`}
                      >
                        {message.sender_type === 'admin' ? 'Support Team' : 'You'} • {formatTimestamp(message.created_at)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              {selectedTicket.status !== 'closed' && (
                <div className="border-t pt-4">
                  <div className="flex gap-2">
                    <Textarea
                      placeholder="Type your message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="flex-1 min-h-[80px]"
                      disabled={isSending}
                    />
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={isSending || !newMessage.trim()}
                      className="self-end"
                    >
                      {isSending ? 'Sending...' : 'Send'}
                    </Button>
                  </div>
                </div>
              )}
              
              {selectedTicket.status === 'closed' && (
                <div className="border-t pt-4 text-center">
                  <p className="text-gray-500 text-sm">This ticket is closed and cannot receive new messages.</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Quick Links */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">FAQ</h3>
            <p className="text-sm text-gray-500">Find answers to common questions</p>
            <Button variant="link" className="p-0 h-auto mt-2">View FAQ →</Button>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Email Support</h3>
            <p className="text-sm text-gray-500"><EMAIL></p>
            <Button variant="link" className="p-0 h-auto mt-2">Send Email →</Button>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Phone Support</h3>
            <p className="text-sm text-gray-500">Available 24/7</p>
            <Button variant="link" className="p-0 h-auto mt-2">Call Now →</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 