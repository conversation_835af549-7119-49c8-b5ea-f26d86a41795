"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { AlertTriangle } from "lucide-react"

export default function DefineLoanSection() {
  const [formData, setFormData] = useState({
    productName: "",
    minAmount: "700,000",
    maxAmount: "2,000,000",
    minDuration: "1",
    maxDuration: "6",
    interestRate: "3.5",
    rateType: "% Monthly",
    description: "",
  })

  const handlePreviewClick = () => {
    // Scroll to the review and confirm section
    const reviewSection = document.getElementById('review-and-confirm')
    if (reviewSection) {
      reviewSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div id="define-loan-section" className="max-w-2xl mx-auto p-4">
      <Card className="w-full">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">1</span>
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Define Loan Offer Parameters</h2>
          </div>
        </CardHeader>

        <CardContent className="space-y-8 p-8">
          {/* Loan Product Name */}
          <div className="space-y-2">
            <Label htmlFor="productName" className="text-sm font-medium text-gray-700">
              Loan Product Name
            </Label>
            <Input
              id="productName"
              placeholder="e.g. SME Boost - Monthly"
              value={formData.productName}
              onChange={(e) => setFormData({ ...formData, productName: e.target.value })}
              className="w-full"
            />
          </div>

          {/* Loan Amount Range and Loan Duration Range - Side by Side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Loan Amount Range */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-900">Loan Amount Range</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minAmount" className="text-xs text-gray-600">
                    Minimum
                  </Label>
                  <Input
                    id="minAmount"
                    type="number"
                    min="0"
                    step="1000"
                    placeholder="₦700,000"
                    onChange={(e) => {
                      const value = e.target.value.replace("₦", "");
                      if (parseFloat(value) < 0) {
                        return; // Prevent negative values
                      }
                      setFormData({ ...formData, minAmount: value });
                    }}
                    className="w-full h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-600"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxAmount" className="text-xs text-gray-600">
                    Maximum
                  </Label>
                  <Input
                    id="maxAmount"
                    type="number"
                    min="0"
                    step="1000"
                    placeholder="₦2,000,000"
                    onChange={(e) => {
                      const value = e.target.value.replace("₦", "");
                      if (parseFloat(value) < 0) {
                        return; // Prevent negative values
                      }
                      setFormData({ ...formData, maxAmount: value });
                    }}
                    className="w-full h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-600"
                  />
                </div>
              </div>
            </div>

            {/* Loan Duration Range */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-900">Loan Duration Range</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">Min (months)</Label>
                  <Select
                    value={formData.minDuration}
                    onValueChange={(value) => setFormData({ ...formData, minDuration: value })}
                  >
                    <SelectTrigger className="w-full h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="6">6</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">Max (months)</Label>
                  <Select
                    value={formData.maxDuration}
                    onValueChange={(value) => setFormData({ ...formData, maxDuration: value })}
                  >
                    <SelectTrigger className="w-full h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="6">6</SelectItem>
                      <SelectItem value="12">12</SelectItem>
                      <SelectItem value="18">18</SelectItem>
                      <SelectItem value="24">24</SelectItem>
                      <SelectItem value="36">36</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* Interest Rate */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">Interest Rate</Label>
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex-1">
                <Input
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="5.5"
                  onChange={(e) => {
                    const value = e.target.value;
                    if (parseFloat(value) < 0) {
                      return; // Prevent negative values
                    }
                    setFormData({ ...formData, interestRate: value });
                  }}
                  className="w-full"
                />
              </div>
              <div className="w-full sm:w-40">
                <Select
                  value={formData.rateType}
                  onValueChange={(value) => setFormData({ ...formData, rateType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="% Monthly">% Monthly</SelectItem>
                    <SelectItem value="% Annually">% Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Warning Message */}
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <AlertTriangle className="w-4 h-4 text-yellow-600 flex-shrink-0" />
              <span className="text-sm text-yellow-800">Platform cap: Maximum 7% monthly rate allowed</span>
            </div>
          </div>

          {/* Lender Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-gray-700">
              Lender Description (Optional)
            </Label>
            <Textarea
              id="description"
              placeholder="e.g. Great for startups, founders with 6-month cash cycles"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full min-h-[80px] resize-none"
            />
          </div>

          {/* Preview Button */}
          <div className="flex justify-end pt-4">
            <Button 
              className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              onClick={handlePreviewClick}
            >
              Preview Loan Offer
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
