import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          user_type: 'individual' | 'corporate'
          mode: 'borrower' | 'lender'
          is_admin: boolean
          admin_level: 'super_admin' | 'admin' | 'moderator' | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          user_type: 'individual' | 'corporate'
          mode?: 'borrower' | 'lender'
          is_admin?: boolean
          admin_level?: 'super_admin' | 'admin' | 'moderator' | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          user_type?: 'individual' | 'corporate'
          mode?: 'borrower' | 'lender'
          is_admin?: boolean
          admin_level?: 'super_admin' | 'admin' | 'moderator' | null
          created_at?: string
          updated_at?: string
        }
      }
      individual_accounts: {
        Row: {
          id: string
          user_id: string
          email: string
          bvn: string | null
          date_of_birth: string | null
          full_name: string | null
          phone_number: string | null
          verification_status: 'pending' | 'verified' | 'rejected'
          employer: string | null
          position: string | null
          monthly_income: number | null
          employment_type: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null
          guarantor_name: string | null
          guarantor_relationship: string | null
          guarantor_phone: string | null
          guarantor_email: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          bvn?: string | null
          date_of_birth?: string | null
          full_name?: string | null
          phone_number?: string | null
          verification_status?: 'pending' | 'verified' | 'rejected'
          employer?: string | null
          position?: string | null
          monthly_income?: number | null
          employment_type?: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null
          guarantor_name?: string | null
          guarantor_relationship?: string | null
          guarantor_phone?: string | null
          guarantor_email?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          bvn?: string | null
          date_of_birth?: string | null
          full_name?: string | null
          phone_number?: string | null
          verification_status?: 'pending' | 'verified' | 'rejected'
          employer?: string | null
          position?: string | null
          monthly_income?: number | null
          employment_type?: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null
          guarantor_name?: string | null
          guarantor_relationship?: string | null
          guarantor_phone?: string | null
          guarantor_email?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      corporate_accounts: {
        Row: {
          id: string
          user_id: string
          email: string
          organization_name: string | null
          office_address: string | null
          approval_status: 'pending' | 'approved' | 'rejected'
          verification_status: 'pending' | 'verified' | 'rejected'
          contact_person: string | null
          contact_phone: string | null
          business_type: string | null
          registration_number: string | null
          tax_identification_number: string | null
          website: string | null
          industry: string | null
          company_size: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null
          years_in_business: number | null
          annual_revenue: number | null
          bank_name: string | null
          account_number: string | null
          account_name: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          organization_name?: string | null
          office_address?: string | null
          approval_status?: 'pending' | 'approved' | 'rejected'
          verification_status?: 'pending' | 'verified' | 'rejected'
          contact_person?: string | null
          contact_phone?: string | null
          business_type?: string | null
          registration_number?: string | null
          tax_identification_number?: string | null
          website?: string | null
          industry?: string | null
          company_size?: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null
          years_in_business?: number | null
          annual_revenue?: number | null
          bank_name?: string | null
          account_number?: string | null
          account_name?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          organization_name?: string | null
          office_address?: string | null
          approval_status?: 'pending' | 'approved' | 'rejected'
          verification_status?: 'pending' | 'verified' | 'rejected'
          contact_person?: string | null
          contact_phone?: string | null
          business_type?: string | null
          registration_number?: string | null
          tax_identification_number?: string | null
          website?: string | null
          industry?: string | null
          company_size?: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null
          years_in_business?: number | null
          annual_revenue?: number | null
          bank_name?: string | null
          account_number?: string | null
          account_name?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      documents: {
        Row: {
          id: string
          user_id: string
          document_type: string
          file_name: string
          file_url: string | null
          status: 'pending' | 'uploaded' | 'verified' | 'rejected'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          document_type: string
          file_name: string
          file_url?: string | null
          status?: 'pending' | 'uploaded' | 'verified' | 'rejected'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          document_type?: string
          file_name?: string
          file_url?: string | null
          status?: 'pending' | 'uploaded' | 'verified' | 'rejected'
          created_at?: string
          updated_at?: string
        }
      }
      admin_users: {
        Row: {
          id: string
          user_id: string
          email: string
          admin_level: 'super_admin' | 'admin' | 'moderator'
          permissions: Record<string, unknown>
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          admin_level: 'super_admin' | 'admin' | 'moderator'
          permissions?: Record<string, unknown>
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          admin_level?: 'super_admin' | 'admin' | 'moderator'
          permissions?: Record<string, unknown>
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      loan_offers: {
        Row: {
          id: string
          lender_id: string
          product_name: string
          min_amount: number
          max_amount: number
          min_duration: number
          max_duration: number
          interest_rate: number
          rate_type: '% Monthly' | '% Annually'
          processing_fee: number
          collateral_required: boolean
          description: string | null
          target_borrowers: string[] | null
          status: 'draft' | 'active' | 'paused' | 'expired'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          lender_id: string
          product_name: string
          min_amount: number
          max_amount: number
          min_duration: number
          max_duration: number
          interest_rate: number
          rate_type: '% Monthly' | '% Annually'
          processing_fee?: number
          collateral_required?: boolean
          description?: string | null
          target_borrowers?: string[] | null
          status?: 'draft' | 'active' | 'paused' | 'expired'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          lender_id?: string
          product_name?: string
          min_amount?: number
          max_amount?: number
          min_duration?: number
          max_duration?: number
          interest_rate?: number
          rate_type?: '% Monthly' | '% Annually'
          processing_fee?: number
          collateral_required?: boolean
          description?: string | null
          target_borrowers?: string[] | null
          status?: 'draft' | 'active' | 'paused' | 'expired'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      loan_applications: {
        Row: {
          id: string
          applicant_id: string
          loan_offer_id: string
          requested_amount: number
          requested_duration: number
          purpose: string
          full_name: string
          email: string
          phone_number: string | null
          date_of_birth: string | null
          employment_status: 'employed' | 'self-employed' | 'unemployed' | 'student' | 'retired'
          employer_name: string | null
          job_title: string | null
          monthly_income: number | null
          work_duration: number | null
          monthly_expenses: number | null
          existing_loans: number | null
          bank_name: string | null
          account_number: string | null
          bvn: string | null
          guarantor_name: string | null
          guarantor_phone: string | null
          guarantor_email: string | null
          guarantor_relationship: string | null
          status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'withdrawn'
          application_date: string
          review_date: string | null
          approval_date: string | null
          rejection_reason: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          applicant_id: string
          loan_offer_id: string
          requested_amount: number
          requested_duration: number
          purpose: string
          full_name: string
          email: string
          phone_number?: string | null
          date_of_birth?: string | null
          employment_status: 'employed' | 'self-employed' | 'unemployed' | 'student' | 'retired'
          employer_name?: string | null
          job_title?: string | null
          monthly_income?: number | null
          work_duration?: number | null
          monthly_expenses?: number | null
          existing_loans?: number | null
          bank_name?: string | null
          account_number?: string | null
          bvn?: string | null
          guarantor_name?: string | null
          guarantor_phone?: string | null
          guarantor_email?: string | null
          guarantor_relationship?: string | null
          status?: 'pending' | 'under_review' | 'approved' | 'rejected' | 'withdrawn'
          application_date?: string
          review_date?: string | null
          approval_date?: string | null
          rejection_reason?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          applicant_id?: string
          loan_offer_id?: string
          requested_amount?: number
          requested_duration?: number
          purpose?: string
          full_name?: string
          email?: string
          phone_number?: string | null
          date_of_birth?: string | null
          employment_status?: 'employed' | 'self-employed' | 'unemployed' | 'student' | 'retired'
          employer_name?: string | null
          job_title?: string | null
          monthly_income?: number | null
          work_duration?: number | null
          monthly_expenses?: number | null
          existing_loans?: number | null
          bank_name?: string | null
          account_number?: string | null
          bvn?: string | null
          guarantor_name?: string | null
          guarantor_phone?: string | null
          guarantor_email?: string | null
          guarantor_relationship?: string | null
          status?: 'pending' | 'under_review' | 'approved' | 'rejected' | 'withdrawn'
          application_date?: string
          review_date?: string | null
          approval_date?: string | null
          rejection_reason?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      support_tickets: {
        Row: {
          id: string
          user_id: string
          subject: string
          category: string
          priority: 'low' | 'medium' | 'high' | 'urgent'
          status: 'open' | 'in_progress' | 'resolved' | 'closed'
          assigned_to: string | null
          created_at: string
          updated_at: string
          resolved_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          subject: string
          category?: string
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
          resolved_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          subject?: string
          category?: string
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
          resolved_at?: string | null
        }
      }
      support_messages: {
        Row: {
          id: string
          ticket_id: string
          sender_id: string
          sender_type: 'user' | 'admin'
          message: string
          is_internal: boolean
          attachments: Record<string, unknown>[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          ticket_id: string
          sender_id: string
          sender_type: 'user' | 'admin'
          message: string
          is_internal?: boolean
          attachments?: Record<string, unknown>[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          ticket_id?: string
          sender_id?: string
          sender_type?: 'user' | 'admin'
          message?: string
          is_internal?: boolean
          attachments?: Record<string, unknown>[]
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
} 